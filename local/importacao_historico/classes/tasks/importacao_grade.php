<?php

namespace local_importacao_historico\tasks;

/**
 * Class importacao
 *
 * @package importacao_historico
 * <AUTHOR> Raphael (PH)
 */
use curl;
use stdClass;

defined('MOODLE_INTERNAL') || die();

/**Teste */
// require_once($CFG->dirroot . '/local/importacao_historico/settings.php');

// $token = get_config('importacao_historico', 'token');
// $urlPlataforma = $CFG->wwwroot;
/**fim teste */

class importacao_grade extends \core\task\scheduled_task
{
    public function get_name()
    {
        return get_string('task_name_grade', 'local_importacao_historico');
    }

    public function execute() {
        global $CFG, $DB;
        require_once($CFG->libdir . '/filelib.php');
        require_once($CFG->dirroot . '/local/importacao_historico/settings.php');

        $token = get_config('importacao_historico', 'token');
        $urlPlataforma = $CFG->wwwroot;
        
        $sql = "SELECT 
            ROW_NUMBER() OVER() AS unique_id,
            c.id as idcourse, 
            u.id as iduser, 
            imp.grade as nota, 
            imp.status_cadastro as status_cadastro, 
            imp.status_matricula as status_matricula, 
            mcm.id as cmid,
            imp.email as email,
            imp.username as username,
            UNIX_TIMESTAMP(DATE_ADD( STR_TO_DATE(data_conclusao,'%d/%m/%Y %H:%i'),INTERVAL 3 HOUR)) AS data_conclusao_timestamp
        FROM {importacao_historico} imp 
        JOIN {course} c ON imp.coursecode = c.idnumber 
        JOIN {user} u ON /*u.email = imp.email OR*/ u.username = imp.username
        JOIN {course_modules} mcm ON mcm.course = c.id
        WHERE mcm.deletioninprogress = 0 and mcm.completionview = 1";

        $results = $DB->get_records_sql($sql);

        // Imprimir os resultados
        foreach ($results as $record) {

            $sql = "SELECT ROW_NUMBER() OVER() AS unique_id, 
            m.name as component, 
            gi.id as idgrade
            FROM {grade_items} gi
            JOIN {modules} m ON gi.itemmodule = m.name
            WHERE gi.courseid = :courseid AND gi.itemtype = 'mod'
            ORDER BY m.name";

            $params = array('courseid' => $record->idcourse);

            $components = $DB->get_records_sql($sql, $params);

            foreach ($components as $component) {

                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => $urlPlataforma . '/webservice/rest/server.php?wsfunction=core_grades_update_grades&moodlewsrestformat=json&wstoken=' . $token,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS => 'source=webservice&courseid=' . $record->idcourse . '&component=mod_' . $component->component . '&activityid=' . $record->cmid . '&itemnumber=0&grades%5B0%5D%5Bstudentid%5D=' . $record->iduser . '&grades%5B0%5D%5Bgrade%5D=' . $record->nota . '',
                    CURLOPT_HTTPHEADER => array(
                        'Content-Type: application/x-www-form-urlencoded',
                        'Cookie: AWSALB=/YXurT3czl2YBoaGPoqrOFfoQ9bisaAzH9b5J1sujRJ4Z0qyMLwDGdpHocEmTh0/rLhJJI2UK5zwSJezSM2JEu6xkiCmop2KX9OtqaiclKNdPxfBZ/FGhwy5L0GV; AWSALBCORS=/YXurT3czl2YBoaGPoqrOFfoQ9bisaAzH9b5J1sujRJ4Z0qyMLwDGdpHocEmTh0/rLhJJI2UK5zwSJezSM2JEu6xkiCmop2KX9OtqaiclKNdPxfBZ/FGhwy5L0GV'
                    ),
                )
                );

                $response = curl_exec($curl);

                curl_close($curl);

                mtrace('inserido a nota: ' . $record->nota . ' no usuário: ' . $record->iduser . ' com a saída:' . $response);

            }
        }

    }
}