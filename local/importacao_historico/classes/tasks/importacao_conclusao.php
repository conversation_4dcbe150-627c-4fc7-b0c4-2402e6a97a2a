<?php

namespace local_importacao_historico\tasks;

/**
 * Class importacao
 *
 * @package importacao_historico
 * <AUTHOR> Leonardo Barcellos
 */

use stdClass;

defined('MOODLE_INTERNAL') || die();

class importacao_conclusao extends \core\task\scheduled_task
{

    public function get_name()
    {
        return get_string('task_name_conclusao', 'local_importacao_historico');
    }

    public function execute() {
        global $DB;

        $sql = "SELECT 
            ROW_NUMBER() OVER() AS unique_id,
            c.id as idcourse, 
            u.id as iduser, 
            imp.status_cadastro as status_cadastro, 
            imp.status_matricula as status_matricula, 
            imp.email as email,
            imp.username as username,
            UNIX_TIMESTAMP(DATE_ADD(STR_TO_DATE(data_conclusao,'%d/%m/%Y %H:%i'), INTERVAL 3 HOUR)) AS data_conclusao_timestamp
        FROM {importacao_historico} imp 
        JOIN {course} c ON imp.coursecode = c.idnumber 
        JOIN {user} u ON /*u.email = imp.email OR*/ u.username = imp.username
        JOIN {course_modules} mcm ON mcm.course = c.id
        JOIN {course_completions} mcc ON mcc.userid = u.id AND mcc.course = c.id
        WHERE mcm.deletioninprogress = 0 AND mcm.completionview = 1";

        $results = $DB->get_records_sql($sql);

        foreach ($results as $record) {

            $courseCompletion = $DB->get_record('course_completions', array('userid' => $record->iduser, 'course' => $record->idcourse));
            $courseCompletion->timecompleted = $record->data_conclusao_timestamp;
            $DB->update_record('course_completions', $courseCompletion);

            // Recupera o registro atualizado para validação
            $updatedCompletion = $DB->get_record('course_completions', array('userid' => $record->iduser, 'course' => $record->idcourse));
            
            // Verifica se a data foi atualizada corretamente
            if ($updatedCompletion->timecompleted == $record->data_conclusao_timestamp) {
                mtrace('Conclusão gerada para o usuário: ' . $record->iduser . ' no Curso: ' . $record->idcourse);
            } else {
                $courseCompletion = $DB->get_record('course_completions', array('userid' => $record->iduser, 'course' => $record->idcourse));
                $courseCompletion->timecompleted = $record->data_conclusao_timestamp;
                $DB->update_record('course_completions', $courseCompletion);
            }
        }
    }
}