<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_importacao_historico\tasks;

/**
 * Scheduled task for user enrollment in courses from historical data.
 *
 * This task processes historical enrollment data and enrolls users in courses
 * using the manual enrollment method. It updates enrollment status after
 * successful enrollment.
 *
 * @package    local_importacao_historico
 * @category   task
 * @copyright  2023 Raphael (PH)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

use Exception;
use dml_exception;
use moodle_exception;

defined('MOODLE_INTERNAL') || die();

class importacao_usuario extends \core\task\scheduled_task
{

    /**
     * Get the name of this task.
     *
     * @return string The task name from language strings
     */
    public function get_name()
    {
        return get_string('task_name_user', 'local_importacao_historico');
    }

    /**
     * Execute the scheduled task.
     *
     * This method processes historical enrollment data and enrolls users in courses.
     * It performs the following operations:
     * 1. Retrieves enrollment data from importacao_historico table
     * 2. Enrolls users in courses using manual enrollment
     * 3. Updates enrollment status in the database
     *
     * @throws dml_exception If database operations fail
     * @throws moodle_exception If enrollment operations fail
     */
    public function execute()
    {
        global $DB;

        mtrace('Iniciando processo de inscrição de usuários...');

        // Optimized SQL query - removed unnecessary ROW_NUMBER() and GROUP BY
        $sql = "SELECT
                    ROW_NUMBER() OVER() AS unique_id,
                    c.id as idcourse,
                    u.id as iduser,
                    imp.grade as nota,
                    c.fullname as coursename,
                    u.username as username,
                    imp.status_cadastro as status_cadastro,
                    imp.status_matricula as status_matricula,
                    imp.email as email
                FROM {importacao_historico} imp
                JOIN {course} c ON imp.coursecode = c.idnumber
                JOIN {user} u ON u.username = imp.username
                LEFT JOIN {enrol} e ON e.courseid = c.id
                LEFT JOIN {user_enrolments} ue ON ue.enrolid = e.id AND ue.userid = u.id
                where
                ue.id IS NULL
                GROUP BY imp.coursecode, u.id, c.id, imp.grade, imp.status_cadastro, imp.status_matricula, imp.email";


        $results = $DB->get_records_sql($sql);

        mtrace('Encontrados ' . count($results) . ' registros para processar.');


        $enrol_manual = enrol_get_plugin('manual');
        if (!$enrol_manual) {
            throw new moodle_exception('error', 'core', '', 'Plugin de inscrição manual não encontrado');
        }

        $processed = 0;
        $errors = 0;

        // Get student role once outside the loop for efficiency
        $studentrole = $DB->get_field('role', 'id', ['shortname' => 'student']);
        if (!$studentrole) {
            throw new moodle_exception('error', 'core', '', 'Role de estudante não encontrada no sistema');
        }

        foreach ($results as $record) {

            try {
                mtrace("Processando usuário: {$record->username} (ID: {$record->iduser}) para curso: {$record->coursename} (ID: {$record->idcourse})");

                // Check if user is already enrolled (improved method)
                if ($this->is_user_enrolled($record->iduser, $record->idcourse)) {
                    mtrace("→ Usuário {$record->username} já está inscrito no curso {$record->coursename}");
                    $this->update_enrollment_status($record->email);
                    $processed++;
                    continue;
                }

                // Get manual enrollment instance for the course
                $manualinstance = $this->get_manual_enrollment_instance($record->idcourse);
                if (!$manualinstance) {
                    mtrace("✗ Instância de inscrição manual não encontrada no curso: {$record->coursename}");
                    $errors++;
                    continue;
                }

                mtrace("→ Tentando inscrever usuário {$record->username} no curso {$record->coursename}...");
                mtrace("   Dados: UserID={$record->iduser}, CourseID={$record->idcourse}, RoleID={$studentrole}, InstanceID={$manualinstance->id}");

                // Enroll user with student role - using time() for start time and 0 for end time (no expiry)
                $enrol_manual->enrol_user($manualinstance, $record->iduser, $studentrole, time(), 0, ENROL_USER_ACTIVE);

                // Verify enrollment was successful
                if ($this->verify_enrollment($record->iduser, $record->idcourse)) {
                    // Update enrollment status efficiently
                    $this->update_enrollment_status($record->email);
                    mtrace("✓ Usuário {$record->username} inscrito com sucesso no curso {$record->coursename}");
                    $processed++;
                } else {
                    mtrace("✗ Falha ao verificar inscrição do usuário {$record->username} no curso {$record->coursename}");
                    // Debug: Check what enrollments exist for this user
                    $this->debug_user_enrollments($record->iduser, $record->idcourse);
                    $errors++;
                }


            } catch (Exception $e) {
                if (isset($transaction)) {
                    $transaction->rollback($e);
                }
                mtrace("✗ Erro ao inscrever usuário {$record->username}: " . $e->getMessage());
                mtrace("   Detalhes: " . $e->getTraceAsString());
                $errors++;
                continue;
            }
        }

        mtrace("Processo concluído: {$processed} usuários inscritos, {$errors} erros.");

    }

    /**
     * Check if user is already enrolled in the course.
     *
     * @param int $userid User ID
     * @param int $courseid Course ID
     * @return bool True if user is enrolled, false otherwise
     */
    private function is_user_enrolled($userid, $courseid)
    {
        global $DB;

        // Get all enrollment instances for the course
        $sql = "SELECT ue.id
                FROM {user_enrolments} ue
                INNER JOIN {enrol} e ON e.id = ue.enrolid
                WHERE ue.userid = :userid
                AND e.courseid = :courseid
                AND ue.status = :status";

        $params = [
            'userid' => $userid,
            'courseid' => $courseid,
            'status' => ENROL_USER_ACTIVE
        ];

        return $DB->record_exists_sql($sql, $params);
    }

    /**
     * Verify if enrollment was successful after enrollment attempt.
     *
     * @param int $userid User ID
     * @param int $courseid Course ID
     * @return bool True if enrollment exists and is active, false otherwise
     */
    private function verify_enrollment($userid, $courseid)
    {
        return $this->is_user_enrolled($userid, $courseid);
    }

    /**
     * Get manual enrollment instance for a course.
     *
     * @param int $courseid Course ID
     * @return object|null Manual enrollment instance or null if not found
     */
    private function get_manual_enrollment_instance($courseid)
    {
        $instances = enrol_get_instances($courseid, true);

        foreach ($instances as $instance) {
            if ($instance->enrol === 'manual') {
                return $instance;
            }
        }

        return null;
    }

    /**
     * Update enrollment status for all records with the given email.
     *
     * @param string $email User email
     * @throws dml_exception If database operation fails
     */
    private function update_enrollment_status($email)
    {
        global $DB;

        $DB->set_field('importacao_historico', 'status_matricula', '1', ['email' => $email]);
    }

    /**
     * Debug method to check user enrollments.
     *
     * @param int $userid User ID
     * @param int $courseid Course ID
     */
    private function debug_user_enrollments($userid, $courseid)
    {
        global $DB;

        mtrace("   DEBUG: Verificando inscrições para usuário {$userid} no curso {$courseid}:");

        // Check all enrollments for this user in this course
        $sql = "SELECT ue.id, ue.status, ue.timestart, ue.timeend, e.enrol, e.status as instance_status
                FROM {user_enrolments} ue
                INNER JOIN {enrol} e ON e.id = ue.enrolid
                WHERE ue.userid = :userid AND e.courseid = :courseid";

        $enrollments = $DB->get_records_sql($sql, ['userid' => $userid, 'courseid' => $courseid]);

        if (empty($enrollments)) {
            mtrace("   DEBUG: Nenhuma inscrição encontrada para este usuário neste curso");
        } else {
            foreach ($enrollments as $enrollment) {
                $status_text = $enrollment->status == ENROL_USER_ACTIVE ? 'ATIVO' : 'SUSPENSO';
                $instance_status_text = $enrollment->instance_status == ENROL_INSTANCE_ENABLED ? 'HABILITADO' : 'DESABILITADO';
                mtrace("   DEBUG: Inscrição ID {$enrollment->id}, Método: {$enrollment->enrol}, Status: {$status_text}, Instância: {$instance_status_text}");
            }
        }

        // Check manual enrollment instances for this course
        $manual_instances = $DB->get_records('enrol', ['courseid' => $courseid, 'enrol' => 'manual']);
        mtrace("   DEBUG: Instâncias de inscrição manual encontradas: " . count($manual_instances));
        foreach ($manual_instances as $instance) {
            $status_text = $instance->status == ENROL_INSTANCE_ENABLED ? 'HABILITADO' : 'DESABILITADO';
            mtrace("   DEBUG: Instância ID {$instance->id}, Status: {$status_text}");
        }
    }
}