<?php

namespace local_importacao_historico\tasks;

/**
 * Class importacao
 *
 * @package importacao_historico
 * <AUTHOR> Raphael (PH)
 */

use curl;
use stdClass;

defined('MOODLE_INTERNAL') || die();

class importacao extends \core\task\scheduled_task
{

    public function get_name()
    {
        return get_string('task_name', 'local_importacao_historico');
    }

    public function execute()
    {
        global $CFG, $DB;
        require_once($CFG->libdir . '/filelib.php');
        require_once($CFG->dirroot . '/local/importacao_historico/settings.php');

        $token = get_config('importacao_historico', 'token');

        $sql = "SELECT 
        ROW_NUMBER() OVER() AS unique_id,
        c.id as idcourse, 
        u.id as iduser, 
        imp.grade as nota, 
        imp.status_cadastro as status_cadastro, 
        imp.status_matricula as status_matricula, 
        mcm.id as cmid,
        imp.email as email,
        imp.username as username,
        UNIX_TIMESTAMP(DATE_ADD( STR_TO_DATE(data_conclusao,'%d/%m/%Y %H:%i'), INTERVAL 3 HOUR)) AS data_conclusao_timestamp
        FROM {importacao_historico} imp 
        JOIN {course} c ON imp.coursecode = c.idnumber 
        JOIN {user} u ON /*u.email = imp.email OR*/ u.username = imp.username
        JOIN {course_modules} mcm ON mcm.course = c.id
        WHERE mcm.deletioninprogress = 0 and mcm.completionview = 1";

        $results = $DB->get_records_sql($sql);


        foreach ($results as $record) {

            $tokenID = $token;
            $domainname = $CFG->wwwroot;
            $functionname = 'core_completion_override_activity_completion_status';
            $restformat = 'json';

            $params = array(
                'cmid' => $record->cmid, // O ID do módulo do curso.                
                'userid' => $record->iduser, // O ID do usuário.
                'newstate' => 1 // O novo estado de conclusão (0: Não concluído, 1: Concluído, 2: Falhou, 3: Concluído com aprovação).
            );

            $serverurl = $domainname . '/webservice/rest/server.php' . '?wstoken=' . $tokenID . '&wsfunction=' . $functionname;

            $curl = new curl();
            $response = $curl->post($serverurl, $params);

            $resposta = json_decode($response, true);


            if (isset($resposta['exception'])) {
                echo "Erro: " . $resposta['message'];
            } else {

                $recordsToUpdate = $DB->get_records('importacao_historico', array('email' => $record->email));

                foreach ($recordsToUpdate as $recordToUpdate) {
                    $data = new stdClass();
                    $data->id = $recordToUpdate->id;
                    $data->status_cadastro = '1';

                    $DB->update_record('importacao_historico', $data);
                } 

                $courseModCompletion = $DB->get_record('course_modules_completion', array('coursemoduleid' => $record->cmid, 'userid' => $record->iduser));

                $courseModCompletion->timemodified = $record->data_conclusao_timestamp;

                $DB->update_record('course_modules_completion', $courseModCompletion);

                mtrace('Conclusão inserida no ModuloID: ' . $record->cmid . ' no usuário: '. $record->iduser);

            }

        }

    }
}