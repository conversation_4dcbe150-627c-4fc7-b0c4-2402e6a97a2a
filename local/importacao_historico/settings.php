<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Plugin settings for the importacao_historico plugin.
 *
 * @package   importacao_historico
 * <AUTHOR> Barcellos 2024
*/

defined('MOODLE_INTERNAL') || die;

if ($hassiteconfig) {

    $settings = new admin_settingpage('local_importacao_historico', new lang_string('pluginname', 'local_importacao_historico'));
    $ADMIN->add('localplugins', $settings);

    $settings->add(new admin_setting_configtext(
        $name = 'importacao_historico/token',
        $visiblename = get_string('importacao_historico:label', 'local_importacao_historico'),
        $description = '<p class="alert-light">Adicionar as funções:</p><ul class="alert-light ml-4">
                <li h6">enrol_manual_enrol_users</li>
                <li>core_completion_override_activity_completion_status</li>
                <li>core_grades_update_grades</li>
            </ul>
        ',
        $defaultsetting = NULL
    ));

    //Adiciona campo com download de arquivo exemplo
    $url_example = $CFG->wwwroot . '/local/importacao_historico/example.xlsx';
    $html = '<div class="alert alert-light" role="alert">';
    $html .=  get_string('download_xls', 'local_importacao_historico') ;
    $html .= '<a href="' . $url_example . '" target="_blank" class="error">' . get_string('download_button_text', 'local_importacao_historico') . '</a>';
    $html .= '</div>';
    $settings->add(new admin_setting_heading('download_heading', '', $html));

}
