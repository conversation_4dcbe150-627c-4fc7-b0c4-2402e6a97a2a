Após a instalação do plugin, um novo recurso estará acessível em seu site através do seguinte caminho:  
    Administração do site > Plugins locais > importacao_historico
Neste local, você encontrará um campo destinado à inserção do token necessário para executar a migração de histórico. Este token deve incluir permissões para utilizar as seguintes APIs:
    enrol_manual_enrol_users
    core_completion_override_activity_completion_status
    core_grades_update_grades
Estas APIs são essenciais para o funcionamento correto da migração de histórico.

Também estará disponível um arquivo .xlsx com todas as colunas necessárias para facilitar a migração


1º
importar arquivo csv para tabela

2º tasks do plugin, executar nessa ordem 

    importacao_usuario
        importacao_grade
            importacao (conclui os modulos)
                importacao_certificado**
                    importacao_conclusao***


3º Após terminar as task do plugin, executar as tasks de conclusão de curso

    Marca a conclusão como iniciada
        Cálculo regular da data de conclusão

Execuções:
cd /public

Execute seguindo essa ordem
1 - importacao_usuario: 
nohup php80 admin/cli/scheduled_task.php --execute='\local_importacao_historico\tasks\importacao_usuario' > output_usuario.log 2>&1
 
2 - importacao_grade /*executar apenas se exigir nota*/
nohup php80 admin/cli/scheduled_task.php --execute='\local_importacao_historico\tasks\importacao_grade' > output_grade.log 2>&1

3 - importacao (conclui os modulos)
nohup php80 admin/cli/scheduled_task.php --execute='\local_importacao_historico\tasks\importacao' > output_modulos.log 2>&1

4 - importacao_certificado /*exeutar apenas se o curso utilizar certificado*/
nohup php80 admin/cli/scheduled_task.php --execute='\local_importacao_historico\tasks\importacao_certificado' > output_certificado.log 2>&1

5 - importacao_conclusao /*caso o curso não tenha certificado, executar para atribuir a data de conclusão*/
nohup php80 admin/cli/scheduled_task.php --execute='\local_importacao_historico\tasks\importacao_conclusao' > output_conclusao.log 2>&1

Após, executar as task's de conclusão de curso

5º - php80 admin/cli/scheduled_task.php --execute='\core\task\completion_daily_task'

6º - php80 admin/cli/scheduled_task.php --execute='\core\task\completion_regular_task'

Obs.: Algumas tasks podem demorar horas para concluir, uma possibilidade é utilizar o NOHUP

Exemplos:

nohup php80 caminho_do_ambiente/public/admin/cli/scheduled_task.php --execute=\\local_importacao_historico\\tasks\\importacao_certificado > output_certificado.log 2>&1

nohup php80 /NFS/www/saneago-homolog.smartlms.com.br/public/admin/cli/scheduled_task.php --execute=\\core\\task\\completion_regular_task > output_certificado.log
